'use client'

import { useAuth } from '@/hooks/useAuth'
import { usePathname } from 'next/navigation'
import { useState } from 'react'

/**
 * Debug component to show auth state - only visible in development
 */
export function AuthDebug() {
  const [isVisible, setIsVisible] = useState(false)
  const { user, profile, loading, isAuthenticated, isAdmin } = useAuth()
  const pathname = usePathname()

  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null
  }

  if (!isVisible) {
    return (
      <button
        onClick={() => setIsVisible(true)}
        className="fixed bottom-4 left-4 z-50 bg-blue-500 text-white px-2 py-1 rounded text-xs"
      >
        Debug Auth
      </button>
    )
  }

  return (
    <div className="fixed bottom-4 left-4 z-50 bg-black/90 text-white p-4 rounded-lg text-xs max-w-sm">
      <div className="flex justify-between items-center mb-2">
        <h3 className="font-bold">Auth Debug</h3>
        <button
          onClick={() => setIsVisible(false)}
          className="text-red-400 hover:text-red-300"
        >
          ×
        </button>
      </div>
      
      <div className="space-y-1">
        <div>
          <strong>Loading:</strong> {loading ? 'Yes' : 'No'}
        </div>
        <div>
          <strong>Authenticated:</strong> {isAuthenticated ? 'Yes' : 'No'}
        </div>
        <div>
          <strong>Admin:</strong> {isAdmin ? 'Yes' : 'No'}
        </div>
        <div>
          <strong>User ID:</strong> {user?.id || 'None'}
        </div>
        <div>
          <strong>Profile Role:</strong> {profile?.role || 'None'}
        </div>
        <div>
          <strong>Current Path:</strong> {pathname}
        </div>
        <div>
          <strong>Timestamp:</strong> {new Date().toLocaleTimeString()}
        </div>
      </div>
    </div>
  )
}
