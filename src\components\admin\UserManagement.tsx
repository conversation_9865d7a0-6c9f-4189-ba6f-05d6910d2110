'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { Plus, Edit, Trash2, Mail, Calendar, Eye, User, Search, Filter, Users, ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight, Download, X } from 'lucide-react'
import Link from 'next/link'
import { UserRole, RoleLevel } from '@/lib/supabase'
import { getRoleArabicName, getAvailableRoles, getRoleBadgeVariant, getAssignableRoles } from '@/lib/roles'
import { useAuth } from '@/hooks/useAuth'

type Profile = {
  id: string
  email: string
  full_name: string | null
  phone: string | null
  role: UserRole
  role_level: RoleLevel
  area_id: string | null
  team_id: string | null
  created_at: string
  updated_at: string
  area?: {
    id: string
    name: string
  } | null
  team?: {
    id: string
    name: string
  } | null
}

interface UserManagementProps {
  users: Profile[]
  onUserChange?: () => void
  showAddButton?: boolean
}

export function UserManagement({ users: initialUsers, onUserChange, showAddButton = true }: UserManagementProps) {
  const { profile: currentUserProfile } = useAuth()
  const [users, setUsers] = useState(initialUsers)
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [editingUser, setEditingUser] = useState<Profile | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')

  // Search and filter states
  const [searchQuery, setSearchQuery] = useState('')
  const [roleFilter, setRoleFilter] = useState('all')
  const [areaFilter, setAreaFilter] = useState('all')
  const [teamFilter, setTeamFilter] = useState('all')
  const [selectedUsers, setSelectedUsers] = useState<string[]>([])

  // Areas and teams for filters
  const [areas, setAreas] = useState<Array<{id: string, name: string}>>([])
  const [teams, setTeams] = useState<Array<{id: string, name: string}>>([])
  const [filtersLoading, setFiltersLoading] = useState(false)

  // Pagination states
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage, setItemsPerPage] = useState(10)

  // Bulk actions states
  const [bulkDeleteDialogOpen, setBulkDeleteDialogOpen] = useState(false)
  const [bulkActionLoading, setBulkActionLoading] = useState(false)

  // Update local users state when initialUsers prop changes
  useEffect(() => {
    setUsers(initialUsers)
  }, [initialUsers])

  // Fetch areas and teams for filters
  useEffect(() => {
    const fetchFiltersData = async () => {
      setFiltersLoading(true)
      try {
        // Fetch areas
        const areasResponse = await fetch('/api/admin/areas')
        if (areasResponse.ok) {
          const areasData = await areasResponse.json()
          setAreas(areasData.areas || [])
        }

        // Fetch teams
        const teamsResponse = await fetch('/api/admin/teams')
        if (teamsResponse.ok) {
          const teamsData = await teamsResponse.json()
          setTeams(teamsData.teams || [])
        }
      } catch (error) {
        console.error('Error fetching filter data:', error)
      } finally {
        setFiltersLoading(false)
      }
    }

    fetchFiltersData()
  }, [])

  // Filter users based on search and filters
  const filteredUsers = users.filter(user => {
    // Search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      const matchesName = user.full_name?.toLowerCase().includes(query)
      const matchesEmail = user.email.toLowerCase().includes(query)
      if (!matchesName && !matchesEmail) return false
    }

    // Role filter
    if (roleFilter !== 'all' && user.role !== roleFilter) return false

    // Area filter
    if (areaFilter !== 'all' && user.area_id !== areaFilter) return false

    // Team filter
    if (teamFilter !== 'all' && user.team_id !== teamFilter) return false

    return true
  })

  // Pagination calculations
  const totalItems = filteredUsers.length
  const totalPages = Math.ceil(totalItems / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const endIndex = startIndex + itemsPerPage
  const paginatedUsers = filteredUsers.slice(startIndex, endIndex)

  // Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1)
  }, [searchQuery, roleFilter, areaFilter, teamFilter])

  // Multi-select functions
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      // Select all users on current page
      const currentPageUserIds = paginatedUsers.map(user => user.id)
      setSelectedUsers(prev => [...new Set([...prev, ...currentPageUserIds])])
    } else {
      // Deselect all users on current page
      const currentPageUserIds = paginatedUsers.map(user => user.id)
      setSelectedUsers(prev => prev.filter(id => !currentPageUserIds.includes(id)))
    }
  }

  const handleUserSelect = (userId: string, checked: boolean) => {
    if (checked) {
      setSelectedUsers(prev => [...prev, userId])
    } else {
      setSelectedUsers(prev => prev.filter(id => id !== userId))
    }
  }

  const clearFilters = () => {
    setSearchQuery('')
    setRoleFilter('all')
    setAreaFilter('all')
    setTeamFilter('all')
    setSelectedUsers([])
    setCurrentPage(1)
  }

  // Pagination functions
  const goToFirstPage = () => setCurrentPage(1)
  const goToLastPage = () => setCurrentPage(totalPages)
  const goToNextPage = () => setCurrentPage(prev => Math.min(prev + 1, totalPages))
  const goToPrevPage = () => setCurrentPage(prev => Math.max(prev - 1, 1))

  const handleItemsPerPageChange = (value: string) => {
    setItemsPerPage(Number(value))
    setCurrentPage(1) // Reset to first page when changing items per page
  }

  // Bulk actions functions
  const handleBulkExport = () => {
    const selectedUsersData = users.filter(user => selectedUsers.includes(user.id))

    // Create CSV content
    const headers = ['الاسم', 'البريد الإلكتروني', 'رقم الهاتف', 'الدور', 'المنطقة', 'الفريق', 'تاريخ الإنشاء']
    const csvContent = [
      headers.join(','),
      ...selectedUsersData.map(user => [
        user.full_name || 'غير محدد',
        user.email,
        user.phone || 'غير محدد',
        getRoleArabicName(user.role),
        user.area?.name || 'غير محدد',
        user.team?.name || 'غير محدد',
        new Date(user.created_at).toLocaleDateString('ar-SA')
      ].join(','))
    ].join('\n')

    // Create and download file
    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `المستخدمين_المحددين_${new Date().toISOString().split('T')[0]}.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    setSuccess(`تم تصدير ${selectedUsers.length} مستخدم بنجاح`)
    setTimeout(() => setSuccess(''), 3000)
  }

  const handleBulkDelete = async () => {
    setBulkActionLoading(true)
    setError('')
    setSuccess('')

    try {
      const response = await fetch('/api/admin/bulk-delete-users', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userIds: selectedUsers })
      })

      const result = await response.json()

      if (!response.ok) {
        setError('خطأ في حذف المستخدمين: ' + result.error)
        return
      }

      setSuccess(`تم حذف ${selectedUsers.length} مستخدم بنجاح`)
      setSelectedUsers([])
      setBulkDeleteDialogOpen(false)

      // Refresh the users list
      if (onUserChange) {
        onUserChange()
      }

      setTimeout(() => setSuccess(''), 3000)
    } catch (error) {
      setError('حدث خطأ أثناء حذف المستخدمين')
    } finally {
      setBulkActionLoading(false)
    }
  }
  
  const [newUser, setNewUser] = useState({
    email: '',
    password: '',
    full_name: '',
    phone: '',
    role: 'sales_employee' as UserRole
  })

  // Get roles that current user can assign
  const assignableRoles = currentUserProfile ? getAssignableRoles(currentUserProfile.role) : []



  const handleAddUser = async () => {
    setLoading(true)
    setError('')
    setSuccess('')

    try {
      // Call API route to create user (since we need service role key)
      const response = await fetch('/api/admin/create-user', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: newUser.email,
          password: newUser.password,
          full_name: newUser.full_name,
          phone: newUser.phone,
          role: newUser.role
        })
      })

      const result = await response.json()

      if (!response.ok) {
        setError('خطأ في إنشاء المستخدم: ' + result.error)
        return
      }

      // Reset form and close dialog
      setNewUser({ email: '', password: '', full_name: '', phone: '', role: 'sales_employee' })
      setIsAddDialogOpen(false)
      setSuccess('تم إضافة المستخدم بنجاح')

      // Clear success message after 3 seconds
      setTimeout(() => setSuccess(''), 3000)

      // Trigger immediate refresh
      if (onUserChange) {
        onUserChange()
      }
    } catch (err) {
      setError('حدث خطأ غير متوقع')
    } finally {
      setLoading(false)
    }
  }

  const handleEditUser = async () => {
    if (!editingUser) return

    setLoading(true)
    setError('')
    setSuccess('')

    try {
      const response = await fetch('/api/admin/update-user', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: editingUser.id,
          full_name: editingUser.full_name,
          phone: editingUser.phone,
          role: editingUser.role
        })
      })

      const result = await response.json()

      if (!response.ok) {
        setError('خطأ في تحديث المستخدم: ' + result.error)
        return
      }

      setIsEditDialogOpen(false)
      setEditingUser(null)
      setSuccess('تم تحديث المستخدم بنجاح')

      // Clear success message after 3 seconds
      setTimeout(() => setSuccess(''), 3000)

      // Trigger immediate refresh
      if (onUserChange) {
        onUserChange()
      }
    } catch (err) {
      setError('حدث خطأ غير متوقع')
    } finally {
      setLoading(false)
    }
  }

  const handleDeleteUser = async (userId: string) => {
    setLoading(true)
    setError('')
    setSuccess('')

    try {
      // Call API route to delete user
      const response = await fetch('/api/admin/delete-user', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId })
      })

      const result = await response.json()

      if (!response.ok) {
        setError('خطأ في حذف المستخدم: ' + result.error)
        return
      }

      setSuccess('تم حذف المستخدم بنجاح')

      // Clear success message after 3 seconds
      setTimeout(() => setSuccess(''), 3000)

      // Trigger immediate refresh
      if (onUserChange) {
        onUserChange()
      }
    } catch (err) {
      setError('حدث خطأ غير متوقع')
    } finally {
      setLoading(false)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  return (
    <Card className="w-full overflow-hidden">
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle>إدارة المستخدمين</CardTitle>
            <CardDescription>
              عرض وإدارة جميع مستخدمي النظام
            </CardDescription>
          </div>

          <div className="flex gap-2">
            <Link href="/dashboard/assign-users">
              <Button variant="outline" className="cursor-pointer">
                <User className="h-4 w-4 ml-2" />
                تعيين المستخدمين
              </Button>
            </Link>

            {showAddButton && (
              <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
                <DialogTrigger asChild>
                  <Button>
                    <Plus className="h-4 w-4 ml-2" />
                    إضافة مستخدم
                  </Button>
                </DialogTrigger>
            <DialogContent className="sm:max-w-[425px] text-right" dir="rtl">
              <DialogHeader className="text-right">
                <DialogTitle className="text-right">إضافة مستخدم جديد</DialogTitle>
                <DialogDescription className="text-right">
                  أدخل بيانات المستخدم الجديد
                </DialogDescription>
              </DialogHeader>
              
              <div className="space-y-4">
                {error && (
                  <div className="text-sm text-destructive bg-destructive/10 p-2 rounded text-right" dir="rtl">
                    {error}
                  </div>
                )}

                {success && (
                  <div className="text-sm text-green-800 bg-green-100 p-2 rounded text-right" dir="rtl">
                    {success}
                  </div>
                )}
                
                <div className="space-y-2">
                  <Label htmlFor="email" className="text-right block">البريد الإلكتروني</Label>
                  <Input
                    id="email"
                    type="email"
                    value={newUser.email}
                    onChange={(e) => setNewUser({ ...newUser, email: e.target.value })}
                    placeholder="أدخل البريد الإلكتروني"
                    className="text-right"
                    dir="rtl"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="password" className="text-right block">كلمة المرور</Label>
                  <Input
                    id="password"
                    type="password"
                    value={newUser.password}
                    onChange={(e) => setNewUser({ ...newUser, password: e.target.value })}
                    placeholder="أدخل كلمة المرور"
                    className="text-right"
                    dir="rtl"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="full_name" className="text-right block">الاسم الكامل</Label>
                  <Input
                    id="full_name"
                    value={newUser.full_name}
                    onChange={(e) => setNewUser({ ...newUser, full_name: e.target.value })}
                    placeholder="أدخل الاسم الكامل"
                    className="text-right"
                    dir="rtl"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="phone" className="text-right block">رقم الهاتف</Label>
                  <Input
                    id="phone"
                    type="tel"
                    value={newUser.phone}
                    onChange={(e) => setNewUser({ ...newUser, phone: e.target.value })}
                    placeholder="أدخل رقم الهاتف"
                    className="text-right"
                    dir="rtl"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="role" className="text-right block">الدور</Label>
                  <Select value={newUser.role} onValueChange={(value: UserRole) => setNewUser({ ...newUser, role: value })} dir="rtl">
                    <SelectTrigger className="text-right" dir="rtl">
                      <SelectValue placeholder="اختر الدور" />
                    </SelectTrigger>
                    <SelectContent className="text-right" dir="rtl">
                      {assignableRoles.map((role) => (
                        <SelectItem key={role} value={role} className="text-right">
                          {getRoleArabicName(role)}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              <div className="flex justify-start gap-2 mt-6" dir="rtl">
                <Button onClick={handleAddUser} disabled={loading}>
                  {loading ? 'جاري الإضافة...' : 'إضافة'}
                </Button>
                <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                  إلغاء
                </Button>
              </div>
            </DialogContent>
          </Dialog>
          )}
          </div>
        </div>
      </CardHeader>

      {/* Global success/error messages */}
      {(success || error) && (
        <div className="px-6 pb-4" dir="rtl">
          {error && (
            <div className="text-sm text-destructive bg-destructive/10 p-3 rounded-md mb-2 text-right">
              {error}
            </div>
          )}
          {success && (
            <div className="text-sm text-green-800 bg-green-100 p-3 rounded-md text-right">
              {success}
            </div>
          )}
        </div>
      )}

      {/* Search and Filters */}
      <div className="px-6 pb-4 space-y-4" dir="rtl">
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            type="text"
            placeholder="ابحث بالاسم أو البريد الإلكتروني..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 text-right"
            dir="rtl"
          />
        </div>

        {/* Filters */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <Label htmlFor="role-filter">الدور</Label>
            <Select value={roleFilter} onValueChange={setRoleFilter}>
              <SelectTrigger className="text-right" dir="rtl">
                <SelectValue placeholder="جميع الأدوار" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع الأدوار</SelectItem>
                <SelectItem value="system_admin">مدير النظام</SelectItem>
                <SelectItem value="area_manager">مدير منطقة</SelectItem>
                <SelectItem value="team_manager">مدير فريق</SelectItem>
                <SelectItem value="sales_employee">موظف مبيعات</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="area-filter">المنطقة</Label>
            <Select value={areaFilter} onValueChange={setAreaFilter} disabled={filtersLoading}>
              <SelectTrigger className="text-right" dir="rtl">
                <SelectValue placeholder="جميع المناطق" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع المناطق</SelectItem>
                {areas.map((area) => (
                  <SelectItem key={area.id} value={area.id}>
                    {area.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="team-filter">الفريق</Label>
            <Select value={teamFilter} onValueChange={setTeamFilter} disabled={filtersLoading}>
              <SelectTrigger className="text-right" dir="rtl">
                <SelectValue placeholder="جميع الفرق" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع الفرق</SelectItem>
                {teams.map((team) => (
                  <SelectItem key={team.id} value={team.id}>
                    {team.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-end">
            <Button variant="outline" onClick={clearFilters} className="w-full cursor-pointer">
              <Filter className="h-4 w-4 ml-2" />
              مسح الفلاتر
            </Button>
          </div>
        </div>

        {/* Selected users info and actions - Fixed height to prevent table shifting */}
        <div className="h-16 flex items-center">
          {selectedUsers.length > 0 && (
            <div className="flex items-center justify-between bg-blue-50 p-4 rounded-lg w-full">
              <div className="flex items-center gap-4">
                <span className="text-sm text-blue-800 font-medium">
                  تم تحديد {selectedUsers.length} مستخدم
                </span>

                {/* Bulk Actions */}
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleBulkExport}
                    className="cursor-pointer"
                    disabled={bulkActionLoading}
                  >
                    <Download className="h-4 w-4 ml-2" />
                    تصدير
                  </Button>

                  <AlertDialog open={bulkDeleteDialogOpen} onOpenChange={setBulkDeleteDialogOpen}>
                    <AlertDialogTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        className="cursor-pointer text-destructive hover:bg-destructive hover:text-destructive-foreground"
                        disabled={bulkActionLoading}
                      >
                        <Trash2 className="h-4 w-4 ml-2" />
                        حذف
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent className="text-right" dir="rtl">
                      <AlertDialogHeader className="text-right">
                        <AlertDialogTitle className="text-right">تأكيد الحذف الجماعي</AlertDialogTitle>
                        <AlertDialogDescription className="text-right">
                          هل أنت متأكد من حذف {selectedUsers.length} مستخدم؟
                          <br />
                          <strong className="text-destructive">هذا الإجراء لا يمكن التراجع عنه وسيتم حذف جميع بيانات المستخدمين المحددين نهائياً.</strong>
                          <br />
                          <br />
                          للمتابعة، يرجى كتابة "تأكيد الحذف" في الحقل أدناه:
                        </AlertDialogDescription>
                      </AlertDialogHeader>

                      <div className="my-4">
                        <Input
                          placeholder="اكتب: تأكيد الحذف"
                          className="text-right"
                          dir="rtl"
                          id="delete-confirmation"
                        />
                      </div>

                      <AlertDialogFooter className="flex-row-reverse gap-2">
                        <AlertDialogAction
                          onClick={() => {
                            const input = document.getElementById('delete-confirmation') as HTMLInputElement
                            if (input?.value === 'تأكيد الحذف') {
                              handleBulkDelete()
                            } else {
                              setError('يرجى كتابة "تأكيد الحذف" للمتابعة')
                              setTimeout(() => setError(''), 3000)
                            }
                          }}
                          className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                          disabled={bulkActionLoading}
                        >
                          {bulkActionLoading ? 'جاري الحذف...' : 'حذف نهائي'}
                        </AlertDialogAction>
                        <AlertDialogCancel disabled={bulkActionLoading}>إلغاء</AlertDialogCancel>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </div>
              </div>

              <Button
                variant="ghost"
                size="sm"
                onClick={() => setSelectedUsers([])}
                className="cursor-pointer"
                disabled={bulkActionLoading}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          )}
        </div>
      </div>

      <CardContent>
        {loading && (
          <div className="text-center py-4" dir="rtl">
            <div className="inline-flex items-center gap-2 text-muted-foreground">
              <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"></div>
              جاري تحديث البيانات...
            </div>
          </div>
        )}
        <div className="w-full">
          <div className="overflow-x-auto border rounded-lg">
            <Table className="w-full">
            <TableHeader>
              <TableRow>
                <TableHead className="w-[50px] text-center">
                  <div className="flex justify-center">
                    <Checkbox
                      checked={paginatedUsers.length > 0 && paginatedUsers.every(user => selectedUsers.includes(user.id))}
                      onCheckedChange={handleSelectAll}
                      className="cursor-pointer"
                    />
                  </div>
                </TableHead>
                <TableHead className="text-right min-w-[120px]">الاسم</TableHead>
                <TableHead className="text-right min-w-[180px]">البريد الإلكتروني</TableHead>
                <TableHead className="text-right min-w-[100px]">الدور</TableHead>
                <TableHead className="text-right min-w-[100px]">المنطقة</TableHead>
                <TableHead className="text-right min-w-[100px]">الفريق</TableHead>
                <TableHead className="text-right min-w-[120px]">الإجراءات</TableHead>
              </TableRow>
            </TableHeader>
          <TableBody>
            {paginatedUsers.length === 0 && (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-8">
                  <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">لا توجد مستخدمين</h3>
                  <p className="text-muted-foreground">
                    {searchQuery
                      ? `لا توجد نتائج للبحث "${searchQuery}"`
                      : 'لا توجد مستخدمين يطابقون الفلاتر المحددة'
                    }
                  </p>
                  {(searchQuery || roleFilter !== 'all' || areaFilter !== 'all' || teamFilter !== 'all') && (
                    <Button
                      variant="outline"
                      onClick={clearFilters}
                      className="mt-4 cursor-pointer"
                    >
                      مسح الفلاتر
                    </Button>
                  )}
                </TableCell>
              </TableRow>
            )}

            {paginatedUsers.map((user) => (
              <TableRow key={user.id} className="hover:bg-muted/50">
                <TableCell className="text-center">
                  <div className="flex justify-center">
                    <Checkbox
                      checked={selectedUsers.includes(user.id)}
                      onCheckedChange={(checked) => handleUserSelect(user.id, checked as boolean)}
                      className="cursor-pointer"
                    />
                  </div>
                </TableCell>
                <TableCell className="text-right">
                  <span className="truncate max-w-[120px] block" title={user.full_name || 'غير محدد'}>
                    {user.full_name || 'غير محدد'}
                  </span>
                </TableCell>
                <TableCell className="text-right">
                  <div className="flex items-center gap-2 max-w-[180px]">
                    <Mail className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                    <span className="truncate" title={user.email}>
                      {user.email}
                    </span>
                  </div>
                </TableCell>
                <TableCell className="text-right">
                  <Badge variant={getRoleBadgeVariant(user.role)}>
                    {getRoleArabicName(user.role)}
                  </Badge>
                </TableCell>
                <TableCell className="text-right">
                  <span className="truncate max-w-[100px] block" title={user.area?.name || 'غير محدد'}>
                    {user.area?.name || 'غير محدد'}
                  </span>
                </TableCell>
                <TableCell className="text-right">
                  <span className="truncate max-w-[100px] block" title={user.team?.name || 'غير محدد'}>
                    {user.team?.name || 'غير محدد'}
                  </span>
                </TableCell>
                <TableCell className="text-right">
                  <div className="flex gap-2">
                    <Link href={`/dashboard/users/${user.id}`}>
                      <Button variant="outline" size="sm">
                        <Eye className="h-4 w-4" />
                      </Button>
                    </Link>

                    <Dialog open={isEditDialogOpen && editingUser?.id === user.id} onOpenChange={(open) => {
                      setIsEditDialogOpen(open)
                      if (!open) setEditingUser(null)
                    }}>
                      <DialogTrigger asChild>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setEditingUser(user)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="sm:max-w-[425px] text-right" dir="rtl">
                        <DialogHeader className="text-right">
                          <DialogTitle className="text-right">تعديل المستخدم</DialogTitle>
                          <DialogDescription className="text-right">
                            تعديل بيانات المستخدم
                          </DialogDescription>
                        </DialogHeader>
                        
                        {editingUser && (
                          <div className="space-y-4">
                            <div className="space-y-2">
                              <Label htmlFor="edit_full_name" className="text-right block">الاسم الكامل</Label>
                              <Input
                                id="edit_full_name"
                                value={editingUser.full_name || ''}
                                onChange={(e) => setEditingUser({ ...editingUser, full_name: e.target.value })}
                                placeholder="أدخل الاسم الكامل"
                                className="text-right"
                                dir="rtl"
                              />
                            </div>

                            <div className="space-y-2">
                              <Label htmlFor="edit_phone" className="text-right block">رقم الهاتف</Label>
                              <Input
                                id="edit_phone"
                                type="tel"
                                value={editingUser.phone || ''}
                                onChange={(e) => setEditingUser({ ...editingUser, phone: e.target.value })}
                                placeholder="أدخل رقم الهاتف"
                                className="text-right"
                                dir="rtl"
                              />
                            </div>

                            <div className="space-y-2">
                              <Label htmlFor="edit_role" className="text-right block">الدور</Label>
                              <Select value={editingUser.role} onValueChange={(value: UserRole) => setEditingUser({ ...editingUser, role: value })} dir="rtl">
                                <SelectTrigger className="text-right" dir="rtl">
                                  <SelectValue placeholder="اختر الدور" />
                                </SelectTrigger>
                                <SelectContent className="text-right" dir="rtl">
                                  {assignableRoles.map((role) => (
                                    <SelectItem key={role} value={role} className="text-right">
                                      {getRoleArabicName(role)}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </div>
                          </div>
                        )}
                        
                        <div className="flex justify-start gap-2 mt-6" dir="rtl">
                          <Button onClick={handleEditUser} disabled={loading}>
                            {loading ? 'جاري التحديث...' : 'تحديث'}
                          </Button>
                          <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                            إلغاء
                          </Button>
                        </div>
                      </DialogContent>
                    </Dialog>
                    
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button variant="outline" size="sm" className="text-destructive hover:text-destructive">
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent className="text-right" dir="rtl">
                        <AlertDialogHeader className="text-right">
                          <AlertDialogTitle className="text-right">تأكيد الحذف</AlertDialogTitle>
                          <AlertDialogDescription className="text-right">
                            هل أنت متأكد من حذف هذا المستخدم؟ هذا الإجراء لا يمكن التراجع عنه.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter className="flex-row-reverse gap-2">
                          <AlertDialogAction
                            onClick={() => handleDeleteUser(user.id)}
                            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                          >
                            حذف
                          </AlertDialogAction>
                          <AlertDialogCancel>إلغاء</AlertDialogCancel>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
          </div>
        </div>

        {/* Pagination */}
        {totalItems > 0 && (
          <div className="flex items-center justify-between px-4 py-4 border-t" dir="rtl">
            {/* Items per page selector */}
            <div className="flex items-center gap-2">
              <span className="text-sm text-muted-foreground">عرض</span>
              <Select value={itemsPerPage.toString()} onValueChange={handleItemsPerPageChange}>
                <SelectTrigger className="w-[70px] text-center">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="5">5</SelectItem>
                  <SelectItem value="10">10</SelectItem>
                  <SelectItem value="20">20</SelectItem>
                  <SelectItem value="50">50</SelectItem>
                </SelectContent>
              </Select>
              <span className="text-sm text-muted-foreground">من {totalItems} عنصر</span>
            </div>

            {/* Page info and navigation */}
            <div className="flex items-center gap-4">
              <span className="text-sm text-muted-foreground">
                صفحة {currentPage} من {totalPages}
              </span>

              <div className="flex items-center gap-1">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={goToFirstPage}
                  disabled={currentPage === 1}
                  className="cursor-pointer"
                >
                  <ChevronsRight className="h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={goToPrevPage}
                  disabled={currentPage === 1}
                  className="cursor-pointer"
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={goToNextPage}
                  disabled={currentPage === totalPages}
                  className="cursor-pointer"
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={goToLastPage}
                  disabled={currentPage === totalPages}
                  className="cursor-pointer"
                >
                  <ChevronsLeft className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
