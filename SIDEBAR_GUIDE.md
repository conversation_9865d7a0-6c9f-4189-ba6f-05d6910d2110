# دليل نظام الشريط الجانبي - سحابة المدينة

## نظرة عامة

تم تطوير نظام شريط جانبي شامل لتطبيق "سحابة المدينة" باستخدام مكونات Shadcn/ui مع دعم كامل للغة العربية و RTL.

## المكونات الرئيسية

### 1. AppSidebar (`src/components/layout/app-sidebar.tsx`)
الشريط الجانبي الرئيسي الذي يحتوي على:
- **التنقل الرئيسي**: روابط للصفحات المختلفة
- **معلومات المستخدم**: عرض بيانات المستخدم الحالي مع قائمة منسدلة

### 2. DashboardLayout (`src/components/layout/dashboard-layout.tsx`)
مكون التخطيط الذي يجمع بين:
- **الشريط الجانبي**: AppSidebar
- **الرأس العلوي**: يحتوي على breadcrumbs والبحث والإشعارات
- **المحتوى الرئيسي**: منطقة عرض المحتوى

## المميزات

### ✅ دعم RTL كامل
- الشريط الجانبي يظهر على الجانب الأيمن
- النصوص محاذاة لليمين
- الأيقونات والعناصر مرتبة بشكل صحيح

### ✅ التنقل التفاعلي
- قوائم فرعية قابلة للطي والتوسيع
- تمييز الصفحة النشطة
- breadcrumbs تلقائية باللغة العربية

### ✅ تصميم متجاوب
- يتحول إلى sheet على الشاشات الصغيرة
- قابل للطي إلى أيقونات فقط
- دعم اختصارات لوحة المفاتيح (Ctrl+B)

### ✅ أدوار مختلفة
- تنقل مختلف للمدير والمستخدم العادي
- عناصر مخصصة حسب الصلاحيات

## كيفية الاستخدام

### 1. تطبيق التخطيط على صفحة جديدة

```tsx
import { DashboardLayout } from '@/components/layout/dashboard-layout'

export default function MyPage() {
  return (
    <DashboardLayout user={user}>
      <div className="space-y-6">
        <h1>محتوى الصفحة</h1>
        {/* باقي المحتوى */}
      </div>
    </DashboardLayout>
  )
}
```

### 2. إضافة عنصر تنقل جديد

في `app-sidebar.tsx`، أضف العنصر الجديد إلى مصفوفة `adminNavigation` أو `userNavigation`:

```tsx
{
  title: "عنوان الصفحة",
  url: "/path/to/page",
  icon: IconComponent,
  items: [ // اختياري للقوائم الفرعية
    {
      title: "صفحة فرعية",
      url: "/path/to/subpage",
    }
  ]
}
```

### 3. إضافة breadcrumb جديد

في `dashboard-layout.tsx`، أضف المسار الجديد إلى `breadcrumbMap`:

```tsx
const breadcrumbMap: Record<string, string> = {
  '/path/to/page': 'عنوان الصفحة',
  // ...
}
```

## الصفحات المتاحة

### للمستخدم العادي:
- `/dashboard` - الرئيسية
- `/dashboard/projects` - المشاريع
- `/dashboard/tasks` - المهام
- `/dashboard/calendar` - التقويم
- `/dashboard/messages` - الرسائل
- `/dashboard/profile` - الملف الشخصي

### للمدير:
- `/dashboard` - الرئيسية
- `/dashboard/admin/users` - إدارة المستخدمين
- `/dashboard/admin/areas` - إدارة المناطق
- `/dashboard/admin/teams` - إدارة الفرق
- `/dashboard/admin/packages` - إدارة الباقات
- `/dashboard/admin/reports` - التقارير
- `/dashboard/admin/settings` - الإعدادات

## التخصيص

### تغيير ألوان الشريط الجانبي
في `globals.css`، يمكن تعديل متغيرات CSS:

```css
:root {
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  /* ... */
}
```

### إضافة أيقونات جديدة
استخدم مكتبة Lucide React:

```tsx
import { NewIcon } from "lucide-react"
```

### تخصيص سلوك الطي
يمكن تغيير خاصية `collapsible` في AppSidebar:
- `"icon"` - يطوى إلى أيقونات
- `"offcanvas"` - يختفي تماماً
- `"none"` - غير قابل للطي

## الاختصارات

- **Ctrl+B** (أو Cmd+B على Mac): تبديل إظهار/إخفاء الشريط الجانبي

## المتطلبات

تأكد من تثبيت المكونات المطلوبة:

```bash
npx shadcn@latest add sidebar breadcrumb avatar dropdown-menu collapsible checkbox
```

## الملاحظات

- الشريط الجانبي يحفظ حالة الطي في cookies
- يدعم التنقل بلوحة المفاتيح
- متوافق مع قارئات الشاشة
- يعمل مع جميع أحجام الشاشات

## استكشاف الأخطاء

### المشكلة: الشريط الجانبي لا يظهر
**الحل**: تأكد من تطبيق `SidebarProvider` في المستوى الأعلى

### المشكلة: النصوص لا تظهر بشكل صحيح
**الحل**: تأكد من إعداد `dir="rtl"` في `html` tag

### المشكلة: الأيقونات في الاتجاه الخاطئ
**الحل**: استخدم `side="right"` في مكون Sidebar

## التطوير المستقبلي

- إضافة إعدادات تخصيص الشريط الجانبي
- دعم السمات المتعددة (فاتح/داكن)
- إضافة المزيد من أنواع التنقل
- تحسين الأداء للقوائم الكبيرة
