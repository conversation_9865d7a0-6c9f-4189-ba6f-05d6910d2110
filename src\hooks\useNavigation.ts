'use client'

import { useRouter, usePathname } from 'next/navigation'
import { useCallback, useRef } from 'react'
import { useAuth } from './useAuth'

/**
 * Enhanced navigation hook that handles auth-aware navigation
 */
export function useNavigation() {
  const router = useRouter()
  const pathname = usePathname()
  const { isAuthenticated, isAdmin, loading } = useAuth()
  const navigationTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  const navigate = useCallback((path: string, options?: { replace?: boolean; force?: boolean }) => {
    // Clear any pending navigation
    if (navigationTimeoutRef.current) {
      clearTimeout(navigationTimeoutRef.current)
    }

    // Don't navigate if still loading auth state (unless forced)
    if (loading && !options?.force) {
      console.log('Navigation delayed - auth still loading')
      navigationTimeoutRef.current = setTimeout(() => {
        navigate(path, options)
      }, 100)
      return
    }

    // Check if navigation is allowed
    if (!isNavigationAllowed(path, isAuthenticated, isAdmin) && !options?.force) {
      console.warn(`Navigation to ${path} not allowed for current auth state`)
      return
    }

    // Perform navigation
    const method = options?.replace ? 'replace' : 'push'
    console.log(`Navigating to ${path} using ${method}`)
    
    try {
      router[method](path)
    } catch (error) {
      console.error('Navigation error:', error)
      // Fallback to window.location for critical navigation
      if (options?.force) {
        window.location.href = path
      }
    }
  }, [router, loading, isAuthenticated, isAdmin])

  const navigateWithAuth = useCallback((path: string, options?: { replace?: boolean }) => {
    // Wait for auth to be ready before navigating
    if (loading) {
      navigationTimeoutRef.current = setTimeout(() => {
        navigateWithAuth(path, options)
      }, 100)
      return
    }

    navigate(path, options)
  }, [navigate, loading])

  const forceNavigate = useCallback((path: string, options?: { replace?: boolean }) => {
    navigate(path, { ...options, force: true })
  }, [navigate])

  const refresh = useCallback(() => {
    console.log('Refreshing current page')
    router.refresh()
  }, [router])

  const goBack = useCallback(() => {
    console.log('Navigating back')
    router.back()
  }, [router])

  return {
    navigate,
    navigateWithAuth,
    forceNavigate,
    refresh,
    goBack,
    currentPath: pathname,
    canNavigate: (path: string) => isNavigationAllowed(path, isAuthenticated, isAdmin)
  }
}

function isNavigationAllowed(path: string, isAuthenticated: boolean, isAdmin: boolean): boolean {
  // Always allow navigation to login and public pages
  if (path === '/login' || path === '/' || path.startsWith('/unauthorized')) {
    return true
  }

  // Require authentication for protected routes
  if (path.startsWith('/dashboard')) {
    if (!isAuthenticated) {
      return false
    }

    // Require admin for admin routes under dashboard
    if (path.startsWith('/dashboard/admin') && !isAdmin) {
      return false
    }
  }

  return true
}
