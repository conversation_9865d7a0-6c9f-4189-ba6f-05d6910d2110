"use client"

import * as React from "react"
import { usePathname } from "next/navigation"
import { PanelLeft, Bell, Search } from "lucide-react"

import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar"
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import { Separator } from "@/components/ui/separator"
import { AppSidebar } from "./app-sidebar"
import { PageTransition } from "@/components/ui/page-transition"
import { ErrorBoundary } from "@/components/ui/error-boundary"
import { QuickLoading } from "@/components/ui/loading"
import { useAuthSync } from "@/hooks/useAuthSync"

interface DashboardLayoutProps {
  children: React.ReactNode
  user?: {
    name?: string
    email?: string
    avatar?: string
    role?: string
  } | null
}

// Breadcrumb mapping for Arabic pages
const breadcrumbMap: Record<string, string> = {
  '/dashboard': 'الرئيسية',
  '/dashboard/projects': 'المشاريع',
  '/dashboard/projects/active': 'المشاريع النشطة',
  '/dashboard/projects/completed': 'المشاريع المكتملة',
  '/dashboard/projects/new': 'مشروع جديد',
  '/dashboard/tasks': 'المهام',
  '/dashboard/tickets': 'الطلبات',
  '/dashboard/tickets/new': 'طلب جديد',
  '/dashboard/tickets/open': 'الطلبات المفتوحة',
  '/dashboard/tickets/all': 'جميع الطلبات',
  '/dashboard/profile': 'الملف الشخصي',
  '/dashboard/daily-closing': 'التقفيل اليومي',
  '/dashboard/calendar': 'التقويم',
  '/dashboard/messages': 'الرسائل',
  '/dashboard/settings': 'الإعدادات',
  '/dashboard/users': 'إدارة المستخدمين',
  '/dashboard/users/new': 'إضافة مستخدم',
  '/dashboard/users/roles': 'الأدوار والصلاحيات',
  '/dashboard/areas': 'إدارة المناطق',
  '/dashboard/teams': 'إدارة الفرق',
  '/dashboard/assign-users': 'تعيين المستخدمين',
  '/dashboard/packages': 'إدارة الباقات',
  '/dashboard/reports': 'التقارير',
  '/dashboard/reports/activity': 'تقارير النشاط',
  '/dashboard/reports/users': 'تقارير المستخدمين',
  '/dashboard/reports/system': 'إحصائيات النظام',
  '/dashboard/projects': 'المشاريع',
  '/dashboard/projects/new': 'مشروع جديد',
  '/dashboard/projects/archived': 'المشاريع المؤرشفة',
  '/dashboard/calendar': 'التقويم',
  '/dashboard/messages': 'الرسائل',
  '/dashboard/settings/system': 'إعدادات النظام',
  '/dashboard/settings/security': 'إعدادات الأمان',
  '/dashboard/settings/backup': 'النسخ الاحتياطي',
}

function generateBreadcrumbs(pathname: string) {
  const segments = pathname.split('/').filter(Boolean)
  const breadcrumbs = []

  let currentPath = ''
  for (const segment of segments) {
    currentPath += `/${segment}`
    let title = breadcrumbMap[currentPath] || segment

    // Handle dynamic ticket IDs
    if (segments[segments.length - 2] === 'tickets' && segments[segments.length - 1] !== 'new' && segments[segments.length - 1] !== 'open' && segments[segments.length - 1] !== 'all') {
      title = 'تفاصيل الطلب'
    }

    // Handle dynamic profile IDs
    if (segments[segments.length - 2] === 'profile') {
      title = 'الملف الشخصي'
    }

    breadcrumbs.push({
      title,
      href: currentPath,
      isLast: currentPath === pathname
    })
  }

  return breadcrumbs
}

export function DashboardLayout({ children, user }: DashboardLayoutProps) {
  const pathname = usePathname()
  const breadcrumbs = generateBreadcrumbs(pathname)
  const isAdmin = user?.role === 'system_admin'

  // Sync auth state to prevent stale state issues
  useAuthSync()

  return (
    <SidebarProvider>
      <AppSidebar user={user} />
      <SidebarInset>
        {/* Header */}
        <header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear" dir="rtl">
          <div className="flex items-center gap-2 px-4">
            <SidebarTrigger className="-mr-1" />
            <Separator orientation="vertical" className="ml-2 h-4" />
            
            {/* Breadcrumbs */}
            <Breadcrumb>
              <BreadcrumbList>
                {breadcrumbs.map((breadcrumb, index) => (
                  <React.Fragment key={breadcrumb.href}>
                    <BreadcrumbItem className="hidden md:block">
                      {breadcrumb.isLast ? (
                        <BreadcrumbPage>{breadcrumb.title}</BreadcrumbPage>
                      ) : (
                        <BreadcrumbLink href={breadcrumb.href}>
                          {breadcrumb.title}
                        </BreadcrumbLink>
                      )}
                    </BreadcrumbItem>
                    {index < breadcrumbs.length - 1 && (
                      <BreadcrumbSeparator className="hidden md:block" />
                    )}
                  </React.Fragment>
                ))}
              </BreadcrumbList>
            </Breadcrumb>
          </div>

          {/* Header Actions */}
          <div className="mr-auto flex items-center gap-2 px-4">
            {/* Search */}
            <div className="relative hidden md:block">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="البحث..."
                className="w-[200px] pr-8 pl-10 lg:w-[300px] text-right"
                dir="rtl"
              />
            </div>

            {/* Notifications */}
            <Button variant="ghost" size="icon" className="relative">
              <Bell className="h-4 w-4" />
              <span className="absolute -top-1 -left-1 h-3 w-3 rounded-full bg-red-500 text-[10px] font-medium text-white flex items-center justify-center">
                3
              </span>
              <span className="sr-only">الإشعارات</span>
            </Button>

            {/* Mobile Search */}
            <Button variant="ghost" size="icon" className="md:hidden">
              <Search className="h-4 w-4" />
              <span className="sr-only">البحث</span>
            </Button>
          </div>
        </header>

        {/* Main Content */}
        <div className="rtl-scrollbar-content flex flex-1 flex-col gap-4 p-4 pt-0 w-full max-h-[calc(100vh-4rem)]">
          <ErrorBoundary>
            <React.Suspense fallback={<QuickLoading text="جاري تحميل المحتوى..." />}>
              <PageTransition>
                {children}
              </PageTransition>
            </React.Suspense>
          </ErrorBoundary>
        </div>
      </SidebarInset>
    </SidebarProvider>
  )
}
