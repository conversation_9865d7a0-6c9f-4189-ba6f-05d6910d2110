'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { UserAvatar } from '@/components/ui/user-avatar'
import { Separator } from '@/components/ui/separator'
import { Label } from '@/components/ui/label'
import { ArrowLeft, Mail, Phone, Calendar, User, Shield, MapPin, Users } from 'lucide-react'
import { UserRole, RoleLevel } from '@/lib/supabase'
import { getRoleArabicName, getRoleBadgeVariant } from '@/lib/roles'

type Profile = {
  id: string
  email: string
  full_name: string | null
  phone: string | null
  role: UserRole
  role_level: RoleLevel
  created_at: string
  updated_at: string
  team?: {
    id: string
    name: string
    area?: {
      id: string
      name: string
    }
  } | null
  area?: {
    id: string
    name: string
  } | null
}

interface ProfileViewProps {
  userId: string
  showBackButton?: boolean
  showEditButton?: boolean
  onEditClick?: () => void
}

export function ProfileView({ userId, showBackButton = false, showEditButton = false, onEditClick }: ProfileViewProps) {
  const router = useRouter()
  const [user, setUser] = useState<Profile | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')

  const fetchUser = async () => {
    try {
      setLoading(true)
      setError('')
      
      const response = await fetch(`/api/admin/users/${userId}`)
      const result = await response.json()

      if (!response.ok) {
        setError(result.error || 'حدث خطأ في جلب بيانات المستخدم')
        return
      }

      setUser(result.user)
    } catch (err) {
      setError('حدث خطأ غير متوقع')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchUser()
  }, [userId])

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="text-muted-foreground">جاري تحميل بيانات المستخدم...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center space-y-4">
          <p className="text-destructive">{error}</p>
          <div className="flex gap-2 justify-center">
            <Button onClick={fetchUser}>
              إعادة المحاولة
            </Button>
            {showBackButton && (
              <Button variant="outline" onClick={() => router.back()}>
                العودة
              </Button>
            )}
          </div>
        </div>
      </div>
    )
  }

  if (!user) return null

  return (
    <div className="space-y-6" dir="rtl">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          {showBackButton && (
            <Button variant="ghost" size="sm" onClick={() => router.back()}>
              <ArrowLeft className="h-4 w-4 ml-2" />
              العودة
            </Button>
          )}
          <div>
            <h2 className="text-3xl font-bold">ملف المستخدم</h2>
            <p className="text-muted-foreground mt-2">
              عرض بيانات المستخدم
            </p>
          </div>
        </div>
        
        {showEditButton && onEditClick && (
          <Button onClick={onEditClick}>
            <User className="h-4 w-4 ml-2" />
            تعديل الملف
          </Button>
        )}
      </div>

      {/* User Profile Card */}
      <Card>
        <CardHeader>
          <div className="flex items-start gap-6">
            <UserAvatar
              src=""
              alt={user.full_name || 'مستخدم'}
              name={user.full_name}
              size="xl"
            />
            
            <div className="flex-1 space-y-2">
              <div className="flex items-center gap-3">
                <h3 className="text-2xl font-bold">
                  {user.full_name || 'غير محدد'}
                </h3>
                <Badge variant={getRoleBadgeVariant(user.role)}>
                  {getRoleArabicName(user.role)}
                </Badge>
              </div>
              
              <div className="flex items-center gap-2 text-muted-foreground">
                <Mail className="h-4 w-4" />
                <span>{user.email}</span>
              </div>
              
              {user.phone && (
                <div className="flex items-center gap-2 text-muted-foreground">
                  <Phone className="h-4 w-4" />
                  <span>{user.phone}</span>
                </div>
              )}
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* User Details */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              المعلومات الأساسية
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label className="text-sm text-muted-foreground">الاسم الكامل</Label>
              <p className="text-sm font-medium">{user.full_name || 'غير محدد'}</p>
            </div>

            <Separator />

            <div>
              <Label className="text-sm text-muted-foreground">البريد الإلكتروني</Label>
              <p className="text-sm font-medium">{user.email}</p>
            </div>

            <Separator />

            <div>
              <Label className="text-sm text-muted-foreground">رقم الهاتف</Label>
              <p className="text-sm font-medium">{user.phone || 'غير محدد'}</p>
            </div>

            <Separator />

            <div>
              <Label className="text-sm text-muted-foreground">الدور</Label>
              <div className="flex items-center gap-2 mt-1">
                {user.role === 'system_admin' ? (
                  <Shield className="h-4 w-4 text-blue-600" />
                ) : (
                  <User className="h-4 w-4 text-gray-600" />
                )}
                <span className="text-sm font-medium">
                  {getRoleArabicName(user.role)}
                </span>
              </div>
            </div>

            {/* Team Information */}
            {user.team && (
              <>
                <Separator />
                <div>
                  <Label className="text-sm text-muted-foreground">الفريق</Label>
                  <div className="flex items-center gap-2 mt-1">
                    <Users className="h-4 w-4 text-gray-600" />
                    <span className="text-sm font-medium">{user.team.name}</span>
                  </div>
                </div>
              </>
            )}

            {/* Area Information */}
            {(user.area || user.team?.area) && (
              <>
                <Separator />
                <div>
                  <Label className="text-sm text-muted-foreground">المنطقة</Label>
                  <div className="flex items-center gap-2 mt-1">
                    <MapPin className="h-4 w-4 text-gray-600" />
                    <span className="text-sm font-medium">
                      {user.area?.name || user.team?.area?.name}
                    </span>
                  </div>
                </div>
              </>
            )}
          </CardContent>
        </Card>

        {/* Account Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              معلومات الحساب
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label className="text-sm text-muted-foreground">تاريخ إنشاء الحساب</Label>
              <p className="text-sm font-medium">{formatDate(user.created_at)}</p>
            </div>

            <Separator />

            <div>
              <Label className="text-sm text-muted-foreground">آخر تحديث</Label>
              <p className="text-sm font-medium">{formatDate(user.updated_at)}</p>
            </div>

            <Separator />

            <div>
              <Label className="text-sm text-muted-foreground">معرف المستخدم</Label>
              <p className="text-xs font-mono bg-muted p-2 rounded">{user.id}</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
