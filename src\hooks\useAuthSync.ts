'use client'

import { useEffect, useRef } from 'react'
import { useAuth } from './useAuth'
import { useRouter, usePathname } from 'next/navigation'

/**
 * Hook to synchronize auth state with navigation and handle stale state issues
 */
export function useAuthSync() {
  const { user, profile, loading, isAuthenticated, isAdmin } = useAuth()
  const router = useRouter()
  const pathname = usePathname()
  const lastAuthState = useRef<{ user: string | null, profile: string | null, pathname: string }>({ user: null, profile: null, pathname: '' })
  const syncTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  useEffect(() => {
    // Clear any pending sync
    if (syncTimeoutRef.current) {
      clearTimeout(syncTimeoutRef.current)
    }

    // Don't sync if still loading
    if (loading) return

    const currentAuthState = {
      user: user?.id || null,
      profile: profile?.id || null,
      pathname
    }

    // Check if auth state has changed
    const authStateChanged = 
      lastAuthState.current.user !== currentAuthState.user ||
      lastAuthState.current.profile !== currentAuthState.profile

    // Check if we're on the wrong page for current auth state
    const needsRedirect = checkRedirectNeeded(pathname, isAuthenticated, isAdmin)

    if (authStateChanged || needsRedirect) {
      console.log('Auth state sync triggered:', {
        authStateChanged,
        needsRedirect,
        currentState: currentAuthState,
        lastState: lastAuthState.current
      })

      // Debounce the sync to prevent rapid redirects
      syncTimeoutRef.current = setTimeout(() => {
        handleAuthSync(pathname, isAuthenticated, isAdmin, router)
        lastAuthState.current = currentAuthState
      }, 100)
    }

    return () => {
      if (syncTimeoutRef.current) {
        clearTimeout(syncTimeoutRef.current)
      }
    }
  }, [user, profile, loading, pathname, isAuthenticated, isAdmin, router])

  // Force refresh auth state if needed
  const refreshAuth = () => {
    console.log('Forcing auth refresh...')
    // Trigger a re-render by updating the ref
    lastAuthState.current = { user: null, profile: null, pathname: '' }
  }

  return { refreshAuth }
}

function checkRedirectNeeded(pathname: string, isAuthenticated: boolean, isAdmin: boolean): boolean {
  // Skip redirect checks for static files and API routes
  if (pathname.startsWith('/_next/') || pathname.startsWith('/api/') || pathname.includes('.')) {
    return false
  }

  // User is on login page but is authenticated
  if (pathname === '/login' && isAuthenticated) {
    return true
  }

  // User is on protected route but not authenticated
  if (pathname.startsWith('/dashboard') && !isAuthenticated) {
    return true
  }

  // User is on admin route but not admin
  if (pathname.startsWith('/dashboard/admin') && isAuthenticated && !isAdmin) {
    return true
  }

  return false
}

function handleAuthSync(pathname: string, isAuthenticated: boolean, isAdmin: boolean, router: any) {
  // User is authenticated but on login page
  if (pathname === '/login' && isAuthenticated) {
    console.log('Redirecting authenticated user from login to /dashboard')
    router.replace('/dashboard')
    return
  }

  // User is not authenticated but on protected route
  if (pathname.startsWith('/dashboard') && !isAuthenticated) {
    console.log('Redirecting unauthenticated user to login')
    router.replace('/login')
    return
  }

  // User is authenticated but trying to access admin without permissions
  if (pathname.startsWith('/dashboard/admin') && isAuthenticated && !isAdmin) {
    console.log('Redirecting non-admin user to dashboard')
    router.replace('/dashboard')
    return
  }
}
